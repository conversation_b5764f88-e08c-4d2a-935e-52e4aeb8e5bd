package vn.vinclub.shield.controller.internal;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.shield.dto.request.ApiProtectRequest;
import vn.vinclub.shield.dto.response.ApiProtectResponse;
import vn.vinclub.shield.service.ShieldService;
import vn.vinclub.shield.util.ServiceResponse;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/internal")
@RequiredArgsConstructor
public class ShieldInternalController {

    private final ShieldService shieldService;

    @PostMapping("/protect-api")
    public ServiceResponse<ApiProtectResponse> protectApi(@RequestBody ApiProtectRequest request) {
        try (var p = new Profiler(getClass(), "protectApi")) {
            return ServiceResponse.success(shieldService.protectApi(request));
        }
    }
}

