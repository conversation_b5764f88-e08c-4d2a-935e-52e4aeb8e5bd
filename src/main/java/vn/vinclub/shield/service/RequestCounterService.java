package vn.vinclub.shield.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.IntegerCodec;
import org.springframework.stereotype.Service;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.shield.config.ShieldAnalyticsConfigs;
import vn.vinclub.shield.enums.Action;

import java.time.Duration;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class RequestCounterService {

    private final RedissonClient redissonClient;
    private final ShieldAnalyticsConfigs configs;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final DateTimeFormatter HOUR_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHH");
    private static final DateTimeFormatter MINUTE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmm");

    /**
     * Get current time in configured timezone
     */
    private ZonedDateTime getCurrentTime() {
        return ZonedDateTime.now(ZoneId.of(configs.getTimezone()));
    }

    /**
     * Check and increment velocity counter for IP address
     * Returns true if this is a high-frequency pattern (multiple requests in short time)
     */
    @Profiler
    public boolean checkAndIncrementVelocityCounter(String ipAddress, Action action) {
        String key = buildVelocityKey(ipAddress, action);
        RBucket<Integer> counter = redissonClient.getBucket(key, new IntegerCodec());
        
        Integer currentCount = counter.get();
        if (currentCount == null) {
            // First request in this time window
            counter.set(1, Duration.ofMinutes(configs.getVelocity().getTtlMinutes()));
            return false;
        } else {
            // Increment counter
            counter.set(currentCount + 1, Duration.ofMinutes(configs.getVelocity().getTtlMinutes()));
            // High frequency if more than threshold requests in the time window
            return currentCount >= configs.getVelocity().getThreshold();
        }
    }

    /**
     * Check if IP has rapid-fire pattern (multiple requests per minute)
     */
    @Profiler
    public boolean checkRapidFirePattern(String ipAddress, Action action) {
        String minuteKey = buildMinuteKey(ipAddress, action);
        RSet<String> minuteSet = redissonClient.getSet(minuteKey);
        
        String requestId = System.currentTimeMillis() + "_" + Thread.currentThread().threadId();
        minuteSet.add(requestId);
        minuteSet.expire(Duration.ofMinutes(configs.getRapidFire().getTtlMinutes()));

        // Rapid fire if more than threshold requests in the same minute
        return minuteSet.size() > configs.getRapidFire().getThreshold();
    }

    /**
     * Check if there are multiple new indicators detected in short time
     */
    @Profiler
    public boolean checkMultipleNewIndicators(String sourceIdentifier, Action action, String indicatorType) {
        String key = buildNewIndicatorKey(sourceIdentifier, action);
        RScoredSortedSet<String> indicators = redissonClient.getScoredSortedSet(key);
        
        long currentTime = System.currentTimeMillis();
        indicators.add(currentTime, indicatorType);
        indicators.expire(Duration.ofHours(configs.getNewIndicators().getTtlHours()));

        // Remove old entries (older than configured time window)
        long timeWindowAgo = currentTime - TimeUnit.HOURS.toMillis(configs.getNewIndicators().getTtlHours());
        indicators.removeRangeByScore(0, true, timeWindowAgo, true);

        // Multiple new indicators if threshold or more different types in the time window
        return indicators.size() >= configs.getNewIndicators().getThreshold();
    }

    /**
     * Check if activity is happening during off-hours
     */
    @Profiler
    public boolean isOffHoursActivity() {
        int hour = getCurrentTime().getHour();
        return hour >= configs.getOffHours().getStartHour() && hour <= configs.getOffHours().getEndHour();
    }

    /**
     * Check for automated behavior pattern using time-window analysis
     */
    @Profiler
    public boolean checkAutomatedBehaviorPattern(String ipAddress, Action action) {
        String key = buildAutomatedPatternKey(ipAddress, action);
        RScoredSortedSet<String> requests = redissonClient.getScoredSortedSet(key);
        
        long currentTime = System.currentTimeMillis();
        String requestId = currentTime + "_" + Thread.currentThread().threadId();
        requests.add(currentTime, requestId);
        requests.expire(Duration.ofMinutes(configs.getAutomatedBehavior().getTtlMinutes()));

        // Remove old entries (older than analysis window)
        long analysisWindowAgo = currentTime - TimeUnit.MINUTES.toMillis(configs.getAutomatedBehavior().getAnalysisWindowMinutes());
        requests.removeRangeByScore(0, true, analysisWindowAgo, true);

        // Automated behavior if more than threshold requests in analysis window
        return requests.size() > configs.getAutomatedBehavior().getThreshold();
    }

    /**
     * Check if user has unusual activity pattern
     */
    @Profiler
    public boolean checkUnusualUserActivity(String userId, Action action) {
        String key = buildUserActivityKey(userId, action);
        RSet<String> activities = redissonClient.getSet(key);

        String activityMarker = getCurrentTime().format(HOUR_FORMATTER);
        activities.add(activityMarker);
        activities.expire(Duration.ofHours(configs.getUnusualActivity().getTtlHours()));

        // Unusual if active in more than threshold different hours in the time window
        return activities.size() > configs.getUnusualActivity().getThreshold();
    }

    private String buildVelocityKey(String ipAddress, Action action) {
        String timeWindow = getCurrentTime().format(HOUR_FORMATTER);
        return String.format("shield_svc_velocity:%s:%s:%s", action.name(), ipAddress, timeWindow);
    }

    private String buildMinuteKey(String ipAddress, Action action) {
        String minute = getCurrentTime().format(MINUTE_FORMATTER);
        return String.format("shield_svc_minute:%s:%s:%s", action.name(), ipAddress, minute);
    }

    private String buildNewIndicatorKey(String sourceIdentifier, Action action) {
        String hour = getCurrentTime().format(HOUR_FORMATTER);
        return String.format("shield_svc_indicators:%s:%s:%s", action.name(), sourceIdentifier, hour);
    }

    private String buildAutomatedPatternKey(String ipAddress, Action action) {
        return String.format("shield_svc_automated:%s:%s", action.name(), ipAddress);
    }

    private String buildUserActivityKey(String userId, Action action) {
        String date = getCurrentTime().format(DATE_FORMATTER);
        return String.format("shield_svc_user_activity:%s:%s:%s", action.name(), userId, date);
    }
}
