package vn.vinclub.shield.service.impl;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.exceptions.TokenExpiredException;
import com.auth0.jwt.interfaces.DecodedJWT;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.common.util.BaseJsonUtils;
import vn.vinclub.shield.config.ShieldTokenConfig;
import vn.vinclub.shield.dto.*;
import vn.vinclub.shield.enums.Action;
import vn.vinclub.shield.service.RiskAssessmentService;
import vn.vinclub.shield.service.ShieldEvaluationService;
import vn.vinclub.shield.util.TrackingContextUtils;

import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ShieldEvaluationServiceImpl implements ShieldEvaluationService {

    private final BaseJsonUtils jsonUtils;
    private final ShieldTokenConfig shieldTokenConfig;
    private final RiskAssessmentService riskAssessmentService;

    @Value("${shield.device-os.allowlist:}")
    private String deviceOsAllowlist;

    @Value("${shield.ip.allowlist:}")
    private String ipAllowlist;

    @Value("${shield.ip.blocklist:}")
    private String ipBlocklist;

    @Value("${shield.user-agent.blocklist:}")
    private String userAgentBlocklist;

    @Value("${shield.action.protect-list:}")
    private String actionProtectList;

    @Profiler
    @Override
    public ShieldEvaluationResult evaluate(EvaluationData evaluationData) {
        String requestId = evaluationData.getRequestId();
        log.info("[START] requestId={}, action={}, key={}, ip={}",
                requestId, evaluationData.getAction(), evaluationData.getKey(), evaluationData.getIpAddress());

        var action = evaluationData.getAction();

        // Skip when action is unknown
        if (Action.UNKNOWN.equals(action)) {
            log.info("[DECISION] requestId={}, decision=PASS, reason=UNKNOWN_ACTION, action={}",
                    requestId, action);
            return ShieldEvaluationResult.pass();
        }

        // Skip when action is not in protect list
        if (!getActionProtectList().contains(action)) {
            log.info("[DECISION] requestId={}, decision=PASS, reason=ACTION_NOT_IN_PROTECT_LIST, action={}",
                    requestId, action);
            return ShieldEvaluationResult.pass();
        }

        var key = evaluationData.getKey();
        var deviceOs = Optional.ofNullable(evaluationData.getDeviceFingerprint()).map(DeviceFingerprint::getDeviceOs);
        var ipAddress = evaluationData.getIpAddress();
        var userAgent = Optional.ofNullable(evaluationData.getUserAgent());
        var metadata = Optional.ofNullable(evaluationData.getMetadata());
        var holdingTimeInSecond = calculateHoldingTime(evaluationData);

        // Create tracking context for risk assessment
        TrackingContext trackingContext = TrackingContextUtils.fromEvaluationData(evaluationData);

        // 0. Check if deviceOs is allowlisted
        if (deviceOs.isPresent() && getDeviceOsAllowlist().contains(deviceOs.get())) {
            log.info("[DECISION] requestId={}, decision=PASS, reason=DEVICE_OS_ALLOWLISTED, deviceOs={}, action={}",
                    requestId, deviceOs.get(), action);
            return ShieldEvaluationResult.pass();
        }

        // 1. Check if IP is allowlisted
        if (getIpAllowlist().contains(ipAddress)) {
            log.info("[DECISION] requestId={}, decision=PASS, reason=IP_ALLOWLISTED, ip={}, action={}",
                    requestId, ipAddress, action);
            return ShieldEvaluationResult.pass();
        }

        // 2. Check if IP is blocklisted
        if (getIpBlocklist().contains(ipAddress)) {
            log.warn("[DECISION] requestId={}, decision=BLOCK, reason=IP_BLOCKLISTED, ip={}, action={}",
                    requestId, ipAddress, action);
            return ShieldEvaluationResult.notPass(generateShieldToken(evaluationData, holdingTimeInSecond), holdingTimeInSecond);
        }

        // 3. Check if userAgent is blocklisted
        if (userAgent.isPresent() && getUserAgentBlocklist().stream().anyMatch(userAgent.get()::contains)) {
            log.warn("[DECISION] requestId={}, decision=BLOCK, reason=USER_AGENT_BLOCKLISTED, userAgent={}, action={}",
                    requestId, userAgent.get(), action);
            return ShieldEvaluationResult.notPass(generateShieldToken(evaluationData, holdingTimeInSecond), holdingTimeInSecond);
        }

        // 4. Comprehensive risk assessment
        RiskAssessment riskAssessment = riskAssessmentService.assessRisk(trackingContext);

        // Check if protection is required based on risk assessment
        if (riskAssessment.isRequiresProtection()) {
            log.warn("[DECISION] requestId={}, decision=BLOCK, reason=RISK_ASSESSMENT, riskFound={}, action={}, factors={}",
                    requestId, riskAssessment.getNumberOfRisks(), action, riskAssessment.getRiskFactors().size());
            return ShieldEvaluationResult.notPass(generateShieldToken(evaluationData, holdingTimeInSecond), holdingTimeInSecond);
        }

        // 5. Check by analyze action data
        if (protectByAnalyzeData(action, key, ipAddress, metadata)) {
            log.warn("[DECISION] requestId={}, decision=BLOCK, reason=ANALYZE_DATA_PROTECTION, action={}, key={}",
                    requestId, action.name(), key);
            return ShieldEvaluationResult.notPass(generateShieldToken(evaluationData, holdingTimeInSecond), holdingTimeInSecond);
        }

        // All checks passed - allow the request
        log.info("[DECISION] requestId={}, decision=PASS, reason=ALL_CHECKS_PASSED, action={}, riskScore={}",
                requestId, action, riskAssessment.getNumberOfRisks());
        return ShieldEvaluationResult.pass();
    }

    @Profiler
    @Override
    public ShieldTokenValidationResult validateShieldToken(EvaluationData evaluationData, String shieldToken) {
        String requestId = evaluationData.getRequestId();
        log.debug("Starting validation for requestId={}", requestId);

        // 1. Check if token is provided
        if (!StringUtils.hasText(shieldToken)) {
            log.warn("requestId={}, result=INVALID, reason=TOKEN_BLANK", requestId);
            return ShieldTokenValidationResult.invalid("TOKEN_BLANK");
        }

        try {
            // 2. Decode token without verification first to extract basic info
            DecodedJWT decodedJWT = JWT.decode(shieldToken);
            log.debug("requestId={}, tokenId={}, decoded successfully",
                    requestId, decodedJWT.getId());

            // 3. Verify signature using public key
            Algorithm algorithm = Algorithm.RSA256((RSAPublicKey) shieldTokenConfig.getPublicKey(), null);
            JWTVerifier verifier = JWT.require(algorithm)
                    .withIssuer(shieldTokenConfig.getIssuer())
                    .withAudience(shieldTokenConfig.getAudience())
                    .build();

            // Verify the token signature and standard claims
            DecodedJWT verifiedJWT = verifier.verify(shieldToken);
            log.debug("requestId={}, signature verification successful", requestId);

            // 4. Check if token is still within holding time (based on iat)
            Instant issuedAt = verifiedJWT.getIssuedAt().toInstant();
            Instant expiresAt = verifiedJWT.getExpiresAt().toInstant();
            Instant now = Instant.now();

            // Extract holding time from token claims
            Long holdingTimeInSecond = verifiedJWT.getClaim("holding_time_in_second").asLong();
            if (holdingTimeInSecond == null) {
                holdingTimeInSecond = 0L;
            }

            // Calculate the holding period end time
            Instant holdingEndTime = issuedAt.plusSeconds(holdingTimeInSecond);

            if (now.isBefore(holdingEndTime)) {
                log.warn("requestId={}, result=INVALID, reason=STILL_IN_HOLDING_TIME, " +
                        "holdingEndTime={}, currentTime={}", requestId, holdingEndTime, now);
                return ShieldTokenValidationResult.invalid("STILL_IN_HOLDING_TIME");
            }

            // 5. Verify claims match with evaluation data
            if (!verifyTokenClaims(verifiedJWT, evaluationData, requestId)) {
                return ShieldTokenValidationResult.invalid("CLAIMS_MISMATCH");
            }

            log.info("requestId={}, result=VALID, validRange={}->{}",
                    requestId, issuedAt, expiresAt);
            return ShieldTokenValidationResult.valid(issuedAt, expiresAt);

        } catch (TokenExpiredException e) {
            log.warn("requestId={}, result=INVALID, reason=TOKEN_EXPIRED, error={}",
                    requestId, e.getMessage());
            return ShieldTokenValidationResult.invalid("TOKEN_EXPIRED");
        } catch (JWTVerificationException e) {
            log.warn("requestId={}, result=INVALID, reason=SIGNATURE_VERIFICATION_FAILED, error={}",
                    requestId, e.getMessage());
            return ShieldTokenValidationResult.invalid("SIGNATURE_VERIFICATION_FAILED");
        } catch (Exception e) {
            log.error("requestId={}, result=INVALID, reason=UNEXPECTED_ERROR, error={}",
                    requestId, e.getMessage(), e);
            return ShieldTokenValidationResult.invalid("UNEXPECTED_ERROR");
        }
    }

    @Profiler
    private String generateShieldToken(EvaluationData evaluationData, Long holdingTimeInSecond) {
        try {
            log.debug("Generating shield token for requestId={}, action={}",
                    evaluationData.getRequestId(), evaluationData.getAction());

            // Create RSA256 algorithm with private key
            Algorithm algorithm = Algorithm.RSA256(null, (RSAPrivateKey) shieldTokenConfig.getPrivateKey());

            // Calculate expiration time
            Instant now = Instant.now();
            Instant expiration = now.plusSeconds(shieldTokenConfig.getExpirationSeconds() + holdingTimeInSecond);

            // Generate unique token ID
            String tokenId = UUID.randomUUID().toString();

            // Build JWT token with all EvaluationData fields as claims
            String token = JWT.create()
                    // Standard JWT claims
                    .withJWTId(tokenId)
                    .withIssuer(shieldTokenConfig.getIssuer())
                    .withAudience(shieldTokenConfig.getAudience())
                    .withIssuedAt(now)
                    .withExpiresAt(expiration)

                    // EvaluationData claims
                    .withClaim("request_id", evaluationData.getRequestId())
                    .withClaim("action", evaluationData.getAction() != null ? evaluationData.getAction().name() : null)
                    .withClaim("key", evaluationData.getKey())
                    .withClaim("ip_address", evaluationData.getIpAddress())
                    .withClaim("user_agent", evaluationData.getUserAgent())

                    // Customer data claims
                    .withClaim("customer_id", evaluationData.getCustomerIdentity() != null ? evaluationData.getCustomerIdentity().getCustomerId() : null)
                    .withClaim("customer_email", evaluationData.getCustomerIdentity() != null ? evaluationData.getCustomerIdentity().getEmail() : null)
                    .withClaim("customer_phone", evaluationData.getCustomerIdentity() != null ? evaluationData.getCustomerIdentity().getPhone() : null)

                    // Device data claims
                    .withClaim("device_id", evaluationData.getDeviceFingerprint() != null ? evaluationData.getDeviceFingerprint().getDeviceId() : null)

                    // Metadata as JSON string if present
                    .withClaim("metadata", evaluationData.getMetadata() != null ? jsonUtils.toString(evaluationData.getMetadata()) : null)

                    // Additional shield-specific claim
                    .withClaim("holding_time_in_second", holdingTimeInSecond)

                    // Sign the token
                    .sign(algorithm);

            log.debug("Successfully generated shield token for requestId={}, tokenId={}, expiresAt={}",
                    evaluationData.getRequestId(), tokenId, expiration);

            return token;

        } catch (Exception e) {
            log.error("Failed to generate shield token for requestId={}, action={}",
                    evaluationData.getRequestId(), evaluationData.getAction(), e);
            throw new RuntimeException("Failed to generate shield token", e);
        }
    }

    /**
     * Verify that token claims match the provided evaluation data
     */
    @Profiler
    private boolean verifyTokenClaims(DecodedJWT verifiedJWT, EvaluationData evaluationData, String requestId) {
        log.debug("[SHIELD_TOKEN_CLAIMS] Starting claims verification for requestId={}", requestId);

        // Verify basic claims
        if (!verifyStringClaim(verifiedJWT, "request_id", evaluationData.getRequestId(), "request_id", requestId)) {
            return false;
        }

        if (!verifyStringClaim(verifiedJWT, "action",
                evaluationData.getAction() != null ? evaluationData.getAction().name() : null, "action", requestId)) {
            return false;
        }

        if (!verifyStringClaim(verifiedJWT, "key", evaluationData.getKey(), "key", requestId)) {
            return false;
        }

        if (!verifyStringClaim(verifiedJWT, "ip_address", evaluationData.getIpAddress(), "ip_address", requestId)) {
            return false;
        }

        if (!verifyStringClaim(verifiedJWT, "user_agent", evaluationData.getUserAgent(), "user_agent", requestId)) {
            return false;
        }

        // Verify customer identity claims
        if (evaluationData.getCustomerIdentity() != null) {
            if (!verifyStringClaim(verifiedJWT, "customer_id",
                    evaluationData.getCustomerIdentity().getCustomerId(), "customer_id", requestId)) {
                return false;
            }

            if (!verifyStringClaim(verifiedJWT, "customer_email",
                    evaluationData.getCustomerIdentity().getEmail(), "customer_email", requestId)) {
                return false;
            }

            if (!verifyStringClaim(verifiedJWT, "customer_phone",
                    evaluationData.getCustomerIdentity().getPhone(), "customer_phone", requestId)) {
                return false;
            }
        }

        // Verify device fingerprint claims
        if (evaluationData.getDeviceFingerprint() != null) {
            if (!verifyStringClaim(verifiedJWT, "device_id",
                    evaluationData.getDeviceFingerprint().getDeviceId(), "device_id", requestId)) {
                return false;
            }
        }

        // Verify metadata claim (as JSON string)
        String expectedMetadata = evaluationData.getMetadata() != null ?
                jsonUtils.toString(evaluationData.getMetadata()) : null;
        if (!verifyStringClaim(verifiedJWT, "metadata", expectedMetadata, "metadata", requestId)) {
            return false;
        }

        log.debug("[SHIELD_TOKEN_CLAIMS] All claims verified successfully for requestId={}", requestId);
        return true;
    }

    /**
     * Helper method to verify string claims
     */
    private boolean verifyStringClaim(DecodedJWT jwt, String claimName, String expectedValue, String fieldName, String requestId) {
        String actualValue = jwt.getClaim(claimName).asString();

        // Handle null values - both should be null or both should have the same value
        if (expectedValue == null && actualValue == null) {
            return true;
        }

        if (expectedValue == null || actualValue == null) {
            log.warn("[TOKEN_CLAIMS] requestId={}, field={}, mismatch=NULL_VALUE, expected={}, actual={}",
                    requestId, fieldName, expectedValue, actualValue);
            return false;
        }

        if (!expectedValue.equals(actualValue)) {
            log.warn("[TOKEN_CLAIMS] requestId={}, field={}, mismatch=VALUE, expected={}, actual={}",
                    requestId, fieldName, expectedValue, actualValue);
            return false;
        }

        return true;
    }

    /**
     * Helper method to verify long claims
     */
    private boolean verifyLongClaim(DecodedJWT jwt, String claimName, Long expectedValue, String fieldName, String requestId) {
        Long actualValue = jwt.getClaim(claimName).asLong();

        // Handle null values
        if (expectedValue == null && actualValue == null) {
            return true;
        }

        if (expectedValue == null || actualValue == null) {
            log.warn("[TOKEN_CLAIMS] requestId={}, field={}, mismatch=NULL_VALUE, expected={}, actual={}",
                    requestId, fieldName, expectedValue, actualValue);
            return false;
        }

        if (!expectedValue.equals(actualValue)) {
            log.warn("[TOKEN_CLAIMS] requestId={}, field={}, mismatch=VALUE, expected={}, actual={}",
                    requestId, fieldName, expectedValue, actualValue);
            return false;
        }

        return true;
    }

    @Profiler
    private Long calculateHoldingTime(EvaluationData evaluationData) {
        // TODO: implement by logic for each action
        if (evaluationData.getAction() == Action.REGISTER_CHECK) {
            return 5L;
        }
        return 0L;
    }

    @Profiler
    private boolean protectByAnalyzeData(Action action, String key, String ipAddress, Optional<Map<String, Object>> metadata) {
        // TODO: implement by logic for each action
        return false;
    }

    private Set<String> getDeviceOsAllowlist() {
        if (!StringUtils.hasText(deviceOsAllowlist)) {
            return Collections.emptySet();
        }

        return Set.of(deviceOsAllowlist.replaceAll(" ", "").split(","));
    }

    private Set<String> getIpAllowlist() {
        if (!StringUtils.hasText(ipAllowlist)) {
            return Collections.emptySet();
        }

        return Set.of(ipAllowlist.replaceAll(" ", "").split(","));
    }

    private Set<String> getIpBlocklist() {
        if (!StringUtils.hasText(ipBlocklist)) {
            return Collections.emptySet();
        }

        return Set.of(ipBlocklist.replaceAll(" ", "").split(","));
    }

    private Set<String> getUserAgentBlocklist() {
        if (!StringUtils.hasText(userAgentBlocklist)) {
            return Collections.emptySet();
        }

        return Set.of(userAgentBlocklist.replaceAll(" ", "").split(","));
    }

    private Set<Action> getActionProtectList() {
        if (!StringUtils.hasText(actionProtectList)) {
            return Collections.emptySet();
        }

        return Arrays.stream(actionProtectList.replaceAll(" ", "").split(","))
                .map(Action::fromString)
                .collect(Collectors.toSet());
    }
}
