package vn.vinclub.shield.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBloomFilter;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.shield.enums.Action;
import vn.vinclub.shield.enums.TimePeriod;
import vn.vinclub.shield.enums.TrackingDataType;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * Centralized bloom filter management for all tracking operations
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BloomFilterService {

    private final RedissonClient redissonClient;

    // Configuration
    @Value("${shield.bloom.filter.global.size:2000000}")
    private Long globalFilterSize;

    @Value("${shield.bloom.filter.monthly.size:1000000}")
    private Long monthlyFilterSize;

    @Value("${shield.bloom.filter.daily.size:500000}")
    private Long dailyFilterSize;

    @Value("${shield.bloom.filter.false-positive-rate:0.01}")
    private Double falsePositiveRate;

    /**
     * Check if an identifier exists without adding it
     */
    @Profiler
    public boolean containsIdentifier(String identifier, Action action, TrackingDataType dataType, TimePeriod timePeriod) {
        if (!StringUtils.hasText(identifier)) {
            return false;
        }
        String filterKey = buildFilterKey(action, dataType, timePeriod);
        RBloomFilter<String> filter = getOrCreateFilter(filterKey, timePeriod);

        if (!filter.isExists()) {
            return false;
        }

        return filter.contains(identifier);
    }

    @Profiler
    public boolean notContainsIdentifier(String identifier, Action action, TrackingDataType dataType, TimePeriod timePeriod) {
        if (!StringUtils.hasText(identifier)) {
            return false;
        }
        String filterKey = buildFilterKey(action, dataType, timePeriod);
        RBloomFilter<String> filter = getOrCreateFilter(filterKey, timePeriod);

        if (!filter.isExists()) {
            return true;
        }

        return !filter.contains(identifier);
    }

    @Profiler
    public boolean notContainsAnyIdentifier(List<String> identifiers, Action action, TrackingDataType dataType, TimePeriod timePeriod) {
        if (CollectionUtils.isEmpty(identifiers)) {
            return false;
        }
        String filterKey = buildFilterKey(action, dataType, timePeriod);
        RBloomFilter<String> filter = getOrCreateFilter(filterKey, timePeriod);

        if (!filter.isExists()) {
            return true;
        }

        var count = 0;
        for (String identifier : identifiers) {
            if (StringUtils.hasText(identifier)) {
                if (filter.contains(identifier)) {
                    count++;
                }
            }
        }
        return count == 0;
    }

    @Profiler
    public void addIdentifier(String identifier, Action action, TrackingDataType dataType, TimePeriod timePeriod) {
        if (!StringUtils.hasText(identifier)) {
            return;
        }
        String filterKey = buildFilterKey(action, dataType, timePeriod);
        RBloomFilter<String> filter = getOrCreateFilter(filterKey, timePeriod);

        filter.add(identifier);
        log.debug("[ADD] filter={}, identifier={}", filterKey, identifier);
    }

    @Profiler
    public void addIdentifiers(List<String> identifiers, Action action, TrackingDataType dataType, TimePeriod timePeriod) {
        if (CollectionUtils.isEmpty(identifiers)) {
            return;
        }
        String filterKey = buildFilterKey(action, dataType, timePeriod);
        RBloomFilter<String> filter = getOrCreateFilter(filterKey, timePeriod);

        for (String identifier : identifiers) {
            if (StringUtils.hasText(identifier)) {
                filter.add(identifier);
                log.debug("[ADD] filter={}, identifier={}", filterKey, identifier);
            }
        }
    }

    /**
     * Get or create a bloom filter with appropriate configuration
     */
    private RBloomFilter<String> getOrCreateFilter(String filterKey, TimePeriod timePeriod) {
        RBloomFilter<String> filter = redissonClient.getBloomFilter(filterKey);

        if (!filter.isExists() || filter.getSize() == 0) {
            Long filterSize = getFilterSize(timePeriod);
            filter.tryInit(filterSize, falsePositiveRate);

            // Set TTL for time-based filters
            if (!TimePeriod.ALL_TIME.equals(timePeriod)) {
                Duration ttl = calculateTtl(timePeriod);
                filter.expire(ttl);
            }
            log.info("[CREATED] filterKey={}, filterSize={}, falsePositiveRate={}",
                    filterKey, filterSize, falsePositiveRate);
        }

        return filter;
    }

    /**
     * Build a standardized filter key
     */
    private String buildFilterKey(Action action, TrackingDataType dataType, TimePeriod timePeriod) {
        StringBuilder keyBuilder = new StringBuilder("shield_svc_filter_");
        keyBuilder.append(dataType.getKeyPrefix());

        if (!TimePeriod.ALL_TIME.equals(timePeriod)) {
            keyBuilder.append("_").append(timePeriod.getKeyPrefix());
            String timeSuffix = timePeriod.getTimeSuffix();
            if (!timeSuffix.isEmpty()) {
                keyBuilder.append(":").append(timeSuffix);
            }
        }

        if (action != null) {
            keyBuilder.append(":").append(action.name());
        }

        return keyBuilder.toString();
    }

    /**
     * Get the appropriate filter size based on time period
     */
    private Long getFilterSize(TimePeriod timePeriod) {
        return switch (timePeriod) {
            case ALL_TIME -> globalFilterSize;
            case MONTHLY -> monthlyFilterSize;
            case DAILY -> dailyFilterSize;
            default -> 1000000L;
        };
    }

    /**
     * Calculate TTL for time-based filters
     */
    private Duration calculateTtl(TimePeriod timePeriod) {
        LocalDateTime now = LocalDateTime.now();

        switch (timePeriod) {
            case MONTHLY:
                LocalDate nextMonth = now.toLocalDate().plusMonths(1).withDayOfMonth(1);
                LocalDateTime endOfNextMonth = LocalDateTime.of(nextMonth.withDayOfMonth(nextMonth.lengthOfMonth()), LocalTime.MAX);
                return Duration.between(now, endOfNextMonth);

            case DAILY:
                LocalDateTime endOfTomorrow = LocalDateTime.of(now.toLocalDate().plusDays(1), LocalTime.MAX);
                return Duration.between(now, endOfTomorrow);

            default:
                return Duration.ofDays(1); // Default fallback
        }
    }
}
